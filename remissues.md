flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:25.952489] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-04T23:00:26.003094] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:26.069871] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:27.385632] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:27.418978] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:28.036032] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:28.069143] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-08-04T23:00:28.262546] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:28.352357] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-08-04T23:00:28.385541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.485574] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.518894] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.569934] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.685602] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.752362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.818960] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:29.968987] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.036670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.136564] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.211627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.252787] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.302360] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.385635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.452231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:30.518981] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:31.502270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:31.536334] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:31.569048] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:31.602371] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:31.636530] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T23:00:32.384227] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-08-04T23:00:33.419670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:33.752298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:34.352319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-04T23:00:34.453485] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:34.468926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:34.502238] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:34.535541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-08-04T23:00:34.539560] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:34.586565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ❌ ERROR [2025-08-04T23:00:34.591654] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-04T23:00:34.614795] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #10     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #11     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #12     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #13     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #14     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #15     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #16     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #17     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #18     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #19     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #20     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #21     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #22     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #23     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #24     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #25     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #26     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #27     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #30     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #31     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #32     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #33     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #34     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #35     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #36     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #37     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #38     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #39     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #40     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #41     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #44     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #45     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #46     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #47     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #48     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #49     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #50     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #51     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #52     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #53     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #54     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #55     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #56     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #57     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #58     _invoke (dart:ui/hooks.dart:312:13)
flutter: #59     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #60     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:34.653254] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-04T23:00:35.568960] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:35.638290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-08-04T23:00:35.936452] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:36.452227] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:36.485503] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:36.519472] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-08-04T23:00:36.675211] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:36.706631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: ❌ ERROR [2025-08-04T23:00:36.713324] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-04T23:00:36.728310] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:36.752419] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: ❌ ERROR [2025-08-04T23:00:36.760435] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:36.853191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:37.764774] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-08-04T23:00:37.786314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-08-04T23:00:39.135660] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:39.302457] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:40.752228] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:40.818878] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:40.868959] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:40.935565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:40.985592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.036491] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.102943] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.152239] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.202133] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.252284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.302285] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.335528] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.402177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.435594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.485991] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.535618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.585754] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.635520] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.702733] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.768882] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.868886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:41.918902] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.019856] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.119818] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.235565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.318910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.369094] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.438363] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.485476] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:42.553091] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:43.718883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:43.785508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:43.852216] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:43.925864] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:43.935549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:43.968928] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.003167] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.052166] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.102243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.168939] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.202453] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.285594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.318806] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.352229] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.402586] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.469766] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.535565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.603208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.653113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.786481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.902377] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:44.969508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.035565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.069844] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.118888] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.168848] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.218890] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.268929] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.319810] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.385480] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.468876] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.519032] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.568895] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.602353] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.668916] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.718933] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.768908] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.818990] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.868880] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.935634] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:45.968964] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.052180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.102207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.168922] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.252331] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.318899] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.586398] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:46.969066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T23:00:47.384013] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-08-04T23:00:48.485829] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:51.035892] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:51.069130] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:51.285870] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-08-04T23:00:51.319621] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:51.353174] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:52.553106] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-04T23:00:52.585531] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:52.636505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-08-04T23:00:52.741730] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:52.802228] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ❌ ERROR [2025-08-04T23:00:52.808318] [FlutterError] 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true. 'package:flutter/src/widgets/basic.dart': Failed assertion: line 333 pos 15: 'opacity >= 0.0 && opacity <= 1.0': is not true.
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      new Opacity (package:flutter/src/widgets/basic.dart:333:15)
flutter: #3      _KaiaAIScreenState._buildExpandedQuickActions.<anonymous closure>.<anonymous closure> (package:culture_connect/screens/kaia_ai_screen.dart:2138:24)
flutter: #4      _TweenAnimationBuilderState.build (package:flutter/src/widgets/tween_animation_builder.dart:207:26)
flutter: #5      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #6      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #7      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #8      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #9      StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #15     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #16     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #17     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #18     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #19     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #21     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     _LayoutBuilderElement._rebuildWithConstraints (package:flutter/src/widgets/layout_builder.dart:239:12)
flutter: #30     RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #31     PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #32     RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #33     RenderConstrainedLayoutBuilder.rebuildIfNecessary (package:flutter/src/widgets/layout_builder.dart:286:5)
flutter: #34     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:377:5)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #37     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #38     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #39     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #40     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #41     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #42     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #43     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #44     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #51     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #52     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #57     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #58     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #62     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #63     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #64     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #65     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #66     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #67     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #68     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #69     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #70     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #71     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #72     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #73     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #74     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #75     _invoke (dart:ui/hooks.dart:312:13)
flutter: #76     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #77     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-08-04T23:00:52.835853] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:52.869026] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:54.152138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:54.335665] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T23:00:59.269047] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-08-04T23:00:59.435440] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T23:01:02.384142] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-08-04T23:01:04.535677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-08-04T23:01:17.383991] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
