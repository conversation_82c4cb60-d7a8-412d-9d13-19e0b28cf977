Performing hot restart...                                               
Restarted application in 2,251ms.
flutter: 🔥 Waiting for Firebase initialization...
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 26ms
flutter: ✅ Firebase core initialized in 35ms
flutter: ✅ Preloaded asset: assets/images/splash.png (18355 bytes)
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 34ms
flutter: ✅ App initialization completed in 52ms
flutter: ✅ Firebase initialization completed successfully
flutter: ✅ Firebase full features initialized in 79ms
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 86ms
flutter: ℹ️ INFO [2025-08-04T22:15:21.934003] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-08-04T22:15:21.937971] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-08-04T22:15:21.938794] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-08-04T22:15:21.951616] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-08-04T22:15:21.963985] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-08-04T22:15:21.976084] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-08-04T22:15:21.981997] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-08-04T22:15:21.970351"}}
flutter: ℹ️ INFO [2025-08-04T22:15:21.993375] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-08-04T22:15:21.993899] [App] All services initialized successfully
flutter: SplashVideoBackground: Video failed to load, using fallback
flutter: VideoBackground: Failed to load video - PlatformException(video_player, *** -[NSURL initFileURLWithPath:]: nil string parameter, null, null)
flutter: 🐛 DEBUG [2025-08-04T22:15:23.415450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":127}
flutter: 🐛 DEBUG [2025-08-04T22:15:23.448406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:23.524058] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:23.631324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:23.715423] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T22:15:26.986678] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-08-04T22:15:30.598443] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:32.987273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":266}
flutter: 🐛 DEBUG [2025-08-04T22:15:33.065206] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-04T22:15:33.132020] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-04T22:15:34.213695] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-08-04T22:15:34.251238] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:34.314167] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-04T22:15:34.348372] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:34.431700] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:36.180798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":350}
flutter: 🐛 DEBUG [2025-08-04T22:15:36.297902] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-08-04T22:15:37.512971] [PerformanceMonitoringService] Slow frame detected {"duration_ms":81}
flutter: 🐛 DEBUG [2025-08-04T22:15:37.614049] [PerformanceMonitoringService] Slow frame detected {"duration_ms":102}
flutter: 🐛 DEBUG [2025-08-04T22:15:37.664055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:39.437302] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-08-04T22:15:39.464950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-08-04T22:15:39.531711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-04T22:15:39.714303] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:39.964402] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:40.780822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:40.964900] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T22:15:41.985829] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🐛 DEBUG [2025-08-04T22:15:42.430629] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:42.947408] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:43.181703] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:43.514073] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:43.714396] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:44.081241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:44.447799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:44.815288] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:44.998377] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:45.898701] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-08-04T22:15:45.931194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":31}
flutter: 🐛 DEBUG [2025-08-04T22:15:46.033625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-08-04T22:15:46.830757] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:48.863854] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:49.048345] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-08-04T22:15:52.374] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-08-04T22:15:52.448420] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-08-04T22:15:52.547285] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: AutoLockService initialized with settings
flutter: Profile screen: Initializing profile...
flutter: Profile screen: Firebase Auth user found: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: Profile screen: _loadUserData called
flutter: currentUserModelProvider: [1754342154097] Starting to fetch user model...
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: Profile screen: Provider is loading, skipping fetch
flutter: Voice service initialized successfully for Kaia AI
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-08-04T22:15:54.511309] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-08-04T22:15:54.512880] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-08-04T22:15:54.514342] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-08-04T22:15:54.516678] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-08-04T22:15:54.523479] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-08-04T22:15:54.533972] [PerformanceMonitoringService] Slow frame detected {"duration_ms":700}
flutter: ❌ ERROR [2025-08-04T22:15:54.654786] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-08-04T22:15:54.660756] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-08-04T22:15:54.665357] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-08-04T22:15:54.688073] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-08-04T22:15:54.688859] [PerformanceMonitoringService] Slow frame detected {"duration_ms":157}
flutter: 🐛 DEBUG [2025-08-04T22:15:54.725502] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-08-04T22:15:54.731179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-08-04T22:15:54.998463] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.030786] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.064045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.121302] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.150313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.203943] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.223290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.310105] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.387010] [PerformanceMonitoringService] Slow frame detected {"duration_ms":70}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.413923] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-08-04T22:15:55.738287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.764220] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.847280] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:55.965406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:56.008175] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-08-04T22:15:56.032006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: ⚠️ WARNING [2025-08-04T22:15:56.986836] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: currentUserModelProvider: AuthService call completed in 3350ms
flutter: currentUserModelProvider: AuthService returned null user model
flutter: currentUserModelProvider: Firebase Auth user: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1, verified: true
flutter: currentUserModelProvider: Firebase Auth user exists but no Firestore document. Creating...
flutter: _createMissingUserDocument: Starting document creation for KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-08-04T22:15:58.684398] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-08-04T22:15:58.830738] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:15:58.947629] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: _createMissingUserDocument: Error creating user document after 2834ms: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: _createMissingUserDocument: Stack trace: #0      FirebaseFirestoreHostApi.documentReferenceGet (package:cloud_firestore_platform_interface/src/pigeon/messages.pigeon.dart:1079:7)
flutter: <asynchronous suspension>
flutter: #1      MethodChannelDocumentReference.get (package:cloud_firestore_platform_interface/src/method_channel/method_channel_document_reference.dart:72:22)
flutter: <asynchronous suspension>
flutter: #2      _JsonDocumentReference.get (package:cloud_firestore/src/document_reference.dart:151:7)
flutter: <asynchronous suspension>
flutter: #3      _createMissingUserDocument (package:culture_connect/providers/auth_provider.dart:163:25)
flutter: <asynchronous suspension>
flutter: #4      Future.timeout.<anonymous closure> (dart:async/future_impl.dart:1004:15)
flutter: <asynchronous suspension>
flutter: #5      currentUserModelProvider.<anonymous closure> (package:culture_connect/providers/auth_provider.dart:102:11)
flutter: <asynchronous suspension>
flutter: #6      FutureHandlerProviderElementMixin.handleFuture.<anonymous closure>.<anonymous closure> (package:riverpod/src/async_notifier/base.dart:355:9)
flutter: <asynchronous suspension>
flutter:
flutter: currentUserModelProvider: Error during document creation: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: currentUserModelProvider: Returning null - no valid user model found
flutter: Profile screen user data: null
flutter: Profile screen: User model is null - creating fallback
flutter: Profile screen: Firebase Auth user: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: Profile screen: Firebase Auth email: <EMAIL>
flutter: Profile screen: Firebase Auth verified: true
flutter: Profile screen: Creating fallback user model from Firebase Auth
flutter: 🐛 DEBUG [2025-08-04T22:16:00.465204] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: DEBUG: _handleQuickAction called for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: false, animValue: 0.0, isVisible: false
flutter: DEBUG: Action buttons NOT visible
flutter: 🐛 DEBUG [2025-08-04T22:16:00.614822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-08-04T22:16:00.647223] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:16:00.680577] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:16:00.713925] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:16:00.798240] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:16:00.847333] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:16:01.114245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: false, animValue: 0.0, isVisible: false
flutter: DEBUG: Action buttons NOT visible
flutter: 🐛 DEBUG [2025-08-04T22:16:02.536612] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-08-04T22:16:02.580681] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-08-04T22:16:02.614895] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: _toggleFabMenu called - current state: false
flutter: DEBUG: FAB menu toggled - new state: true
flutter: DEBUG: Starting forward animation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.0, isVisible: false
flutter: DEBUG: Action buttons NOT visible
flutter: 🐛 DEBUG [2025-08-04T22:16:05.030679] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.30653202533721924, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:05.063956] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.5492968219518661, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:05.097277] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.7226102217286825, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.7848314937204123, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.8348567074537278, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:05.173919] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.9240303764492274, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:05.214725] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.9696188751515001, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.9814908374100924, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.9900656582321972, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 0.9957486020773649, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:05.298031] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:08.947265] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:08.980716] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.047241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.080872] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.113801] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.148004] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.330638] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.364341] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.397337] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.514592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.547237] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.580589] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.630800] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:09.731154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:16:09.780553] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T22:16:11.986308] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-08-04T22:16:17.397332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T22:16:26.986590] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-08-04T22:16:41.985937] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🐛 DEBUG [2025-08-04T22:16:51.514169] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-04T22:16:56.985883] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.097750] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.130344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.163881] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.197068] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.231050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.513969] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: DEBUG: AnimatedBuilder - expanded: true, animValue: 1.0, isVisible: true
flutter: DEBUG: Rendering action buttons
flutter: DEBUG: Building expanded action button for destination
flutter: DEBUG: Building expanded action button for itinerary
flutter: DEBUG: Building expanded action button for flights
flutter: DEBUG: Building expanded action button for accommodation
flutter: 🐛 DEBUG [2025-08-04T22:16:57.547249] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-04T22:17:12.030245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}