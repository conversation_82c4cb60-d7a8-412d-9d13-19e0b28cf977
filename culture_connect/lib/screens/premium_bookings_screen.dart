import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/data/premium_mock_data.dart';
import 'package:culture_connect/widgets/premium_hero_dashboard.dart';
import 'package:culture_connect/widgets/premium_booking_card.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';
import 'package:culture_connect/screens/booking/adaptive_booking_details_screen.dart';

/// Premium booking service provider
final premiumBookingServiceProvider =
    Provider<PremiumBookingService>((ref) => PremiumBookingService());

/// Provider for booking statistics
final premiumBookingStatsProvider =
    FutureProvider<PremiumBookingStats>((ref) async {
  final service = ref.watch(premiumBookingServiceProvider);
  return service.getBookingStats();
});

/// Provider for all bookings
final premiumAllBookingsProvider =
    FutureProvider<List<PremiumBooking>>((ref) async {
  final service = ref.watch(premiumBookingServiceProvider);
  return service.getAllBookings();
});

/// Provider for filtered bookings
final premiumFilteredBookingsProvider =
    Provider.family<AsyncValue<List<PremiumBooking>>, String>((ref, filter) {
  final bookingsAsync = ref.watch(premiumAllBookingsProvider);

  return bookingsAsync.when(
    data: (bookings) {
      List<PremiumBooking> filtered;
      switch (filter) {
        case 'upcoming':
          filtered = bookings
              .where((b) =>
                  b.status == PremiumBookingStatus.upcoming ||
                  b.status == PremiumBookingStatus.confirmed ||
                  b.status == PremiumBookingStatus.pending)
              .toList();
          break;
        case 'completed':
          filtered = bookings
              .where((b) => b.status == PremiumBookingStatus.completed)
              .toList();
          break;
        case 'cancelled':
          filtered = bookings
              .where((b) => b.status == PremiumBookingStatus.cancelled)
              .toList();
          break;
        default:
          filtered = bookings;
      }
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Premium booking screen with stunning visual design
class PremiumBookingsScreen extends ConsumerStatefulWidget {
  const PremiumBookingsScreen({super.key});

  @override
  ConsumerState<PremiumBookingsScreen> createState() =>
      _PremiumBookingsScreenState();
}

class _PremiumBookingsScreenState extends ConsumerState<PremiumBookingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _searchController;
  late Animation<double> _searchAnimation;
  late ScrollController _scrollController; // Added: For better scroll control

  final TextEditingController _searchTextController = TextEditingController();
  String _searchQuery = '';

  bool _isRefreshing = false;
  bool _isSearchExpanded = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);

    _scrollController = ScrollController(); // Initialize scroll controller

    _searchController = AnimationController(
      duration: PremiumDesignTokens.animationMedium,
      vsync: this,
    );

    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchController,
      curve: PremiumDesignTokens.curveEaseOut,
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.dispose(); // Dispose scroll controller
    _searchTextController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) return;
    // Trigger rebuild to update animated indicator position
    setState(() {});
  }

  Future<void> _handleRefresh() async {
    setState(() => _isRefreshing = true);

    // Invalidate providers to trigger refresh
    ref.invalidate(premiumBookingStatsProvider);
    ref.invalidate(premiumAllBookingsProvider);

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1200));

    setState(() => _isRefreshing = false);
  }

  void _toggleSearch() {
    setState(() => _isSearchExpanded = !_isSearchExpanded);
    if (_isSearchExpanded) {
      _searchController.forward();
    } else {
      _searchController.reverse();
      _searchTextController.clear();
      _searchQuery = '';
    }
  }

  void _handleSearch(String query) {
    setState(() => _searchQuery = query);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PremiumDesignTokens.neutralGray50,
      body: Column(
        children: [
          _buildSimpleAppBar(), // Fixed app bar without collapsing behavior
          Expanded(
            child: RefreshIndicator(
              onRefresh: _handleRefresh,
              color: PremiumDesignTokens.primaryBlue,
              backgroundColor: PremiumDesignTokens.neutralWhite,
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    _buildHeroDashboard(),
                    _buildSearchSection(),
                    _buildTabSection(),
                    _buildBookingsList(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAppBar() {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralGray50,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            PremiumDesignTokens.neutralGray50,
            PremiumDesignTokens.neutralGray50.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: PremiumDesignTokens.spacing24,
            vertical: PremiumDesignTokens.spacing16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Bookings',
                style: PremiumDesignTokens.displayMedium.copyWith(
                  color: PremiumDesignTokens.neutralGray900,
                  fontWeight: FontWeight.w800,
                ),
              ),
              Container(
                child: IconButton(
                  onPressed: _toggleSearch,
                  icon: AnimatedSwitcher(
                    duration: PremiumDesignTokens.animationFast,
                    child: Icon(
                      _isSearchExpanded
                          ? Icons.close_rounded
                          : Icons.search_rounded,
                      key: ValueKey(_isSearchExpanded),
                      color: PremiumDesignTokens.primaryBlue,
                      size: 24,
                    ),
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: PremiumDesignTokens.neutralWhite,
                    padding:
                        const EdgeInsets.all(PremiumDesignTokens.spacing12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                          PremiumDesignTokens.radiusMedium),
                    ),
                    elevation: 4,
                    shadowColor:
                        PremiumDesignTokens.primaryBlue.withValues(alpha: 0.2),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeroDashboard() {
    return Container(
      margin: const EdgeInsets.only(top: 8), // Spacing from app bar
      child: Consumer(
        builder: (context, ref, child) {
          final statsAsync = ref.watch(premiumBookingStatsProvider);
          return statsAsync.when(
            data: (stats) => PremiumHeroDashboard(
              stats: stats,
              isLoading: _isRefreshing,
              onRefresh: _handleRefresh,
            ),
            loading: () => const PremiumHeroDashboard(
              stats: PremiumBookingStats(
                totalBookings: 0,
                upcomingBookings: 0,
                completedTrips: 0,
                countriesVisited: 0,
                averageRating: 0.0,
                totalSpent: 0.0,
                totalSaved: 0.0,
                favoriteDestination: '',
                mostBookedType: PremiumBookingType.hotel,
              ),
              isLoading: true,
            ),
            error: (error, stack) => Container(
              margin: const EdgeInsets.all(PremiumDesignTokens.spacing16),
              padding: const EdgeInsets.all(PremiumDesignTokens.spacing24),
              decoration: BoxDecoration(
                color: PremiumDesignTokens.statusError.withValues(alpha: 0.1),
                borderRadius:
                    BorderRadius.circular(PremiumDesignTokens.radiusLarge),
                border: Border.all(
                  color: PremiumDesignTokens.statusError.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline_rounded,
                    color: PremiumDesignTokens.statusError,
                    size: 48,
                  ),
                  const SizedBox(height: PremiumDesignTokens.spacing12),
                  Text(
                    'Unable to load dashboard',
                    style: PremiumDesignTokens.headlineSmall.copyWith(
                      color: PremiumDesignTokens.statusError,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchSection() {
    return AnimatedBuilder(
      animation: _searchAnimation,
      builder: (context, child) {
        return Container(
          height: _searchAnimation.value * 70,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Opacity(
            opacity: _searchAnimation.value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius:
                    BorderRadius.circular(12), // Exact React Native radius
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(31), // rgba(34, 34, 34, 0.12)
                    offset: const Offset(0, 2),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Search Icon - Left
                  const Padding(
                    padding: EdgeInsets.only(left: 16, right: 8),
                    child: Icon(
                      Icons.search,
                      size: 20,
                      color: Color(0xFFB0B0B0), // #B0B0B0
                    ),
                  ),

                  // Text Input
                  Expanded(
                    child: TextField(
                      controller: _searchTextController,
                      onChanged: _handleSearch,
                      style: const TextStyle(
                        fontSize: 16, // 16px
                        color: Color(0xFF222222), // #222222
                        fontWeight: FontWeight.w400,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Search your bookings...',
                        hintStyle: const TextStyle(
                          fontSize: 16,
                          color: Color(0xFFB0B0B0), // #B0B0B0
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),

                  // Clear Button (if text exists)
                  if (_searchQuery.isNotEmpty)
                    GestureDetector(
                      onTap: () {
                        _searchTextController.clear();
                        _handleSearch('');
                      },
                      child: const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Icon(
                          Icons.close,
                          size: 20,
                          color: Color(0xFFB0B0B0),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(
        12, // Reduced from 16px to prevent overflow
        PremiumDesignTokens
            .spacing24, // Added top margin for visual separation from hero card
        12, // Reduced from 16px to prevent overflow
        PremiumDesignTokens.spacing20,
      ),
      child: _buildPremiumSegmentedControl(),
    );
  }

  Widget _buildPremiumSegmentedControl() {
    return Row(
      children: [
        _buildModernTab(
            'Upcoming', Icons.access_time, 0, _getTabCount('upcoming')),
        const SizedBox(width: 8), // Reduced from 12px to prevent overflow
        _buildModernTab('Completed', Icons.check_circle_outline, 1,
            _getTabCount('completed')),
        const SizedBox(width: 8), // Reduced from 12px to prevent overflow
        _buildModernTab(
            'Cancelled', Icons.cancel_outlined, 2, _getTabCount('cancelled')),
      ],
    );
  }

  // Modern tab implementation based on reference image

  Widget _buildModernTab(String title, IconData icon, int index, int count) {
    final isSelected = _tabController.index == index;

    // Define colors based on the reference image
    Color backgroundColor;
    Color textColor;
    Color iconColor;

    if (isSelected) {
      // Active tab styling (red/pink from reference image)
      backgroundColor =
          const Color(0xFFFF385C); // Pink/red color from reference
      textColor = Colors.white;
      iconColor = Colors.white;
    } else {
      // Inactive tab styling (gray from reference image)
      backgroundColor = const Color(0xFFF5F5F5); // Light gray background
      textColor = const Color(0xFF9E9E9E); // Gray text
      iconColor = const Color(0xFF9E9E9E); // Gray icon
    }

    return Expanded(
      child: GestureDetector(
        onTap: () => _tabController.animateTo(index),
        child: Container(
          height: 48,
          margin: const EdgeInsets.symmetric(
              horizontal: 2), // Add margin to prevent overflow
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius:
                BorderRadius.circular(24), // Pill shape from reference
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color:
                          const Color(0xFFFF385C).withAlpha(51), // 20% opacity
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize:
                MainAxisSize.min, // Prevent overflow by using minimum space
            children: [
              Icon(
                icon,
                size: 18,
                color: iconColor,
              ),
              const SizedBox(width: 8),
              Flexible(
                // Use Flexible to prevent text overflow
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                  overflow:
                      TextOverflow.ellipsis, // Handle text overflow gracefully
                  maxLines: 1,
                ),
              ),
              // Removed count badges as requested to match reference image exactly
            ],
          ),
        ),
      ),
    );
  }

  int _getTabCount(String tab) {
    final bookingsAsync = ref.watch(premiumAllBookingsProvider);
    return bookingsAsync.when(
      data: (bookings) {
        switch (tab) {
          case 'upcoming':
            return bookings
                .where((b) =>
                    b.status == PremiumBookingStatus.upcoming ||
                    b.status == PremiumBookingStatus.confirmed ||
                    b.status == PremiumBookingStatus.pending)
                .length;
          case 'completed':
            return bookings
                .where((b) => b.status == PremiumBookingStatus.completed)
                .length;
          case 'cancelled':
            return bookings
                .where((b) => b.status == PremiumBookingStatus.cancelled)
                .length;
          default:
            return 0;
        }
      },
      loading: () => 0,
      error: (_, __) => 0,
    );
  }

  Widget _buildBookingsList() {
    return SizedBox(
      height: 600, // Fixed height for tab content
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildBookingListForTab('upcoming'),
          _buildBookingListForTab('completed'),
          _buildBookingListForTab('cancelled'),
        ],
      ),
    );
  }

  Widget _buildBookingListForTab(String filter) {
    return Consumer(
      builder: (context, ref, child) {
        final filteredBookingsAsync =
            ref.watch(premiumFilteredBookingsProvider(filter));

        return filteredBookingsAsync.when(
          data: (bookings) {
            // Apply search filter
            final searchFiltered = _searchQuery.isEmpty
                ? bookings
                : bookings
                    .where((booking) =>
                        booking.title
                            .toLowerCase()
                            .contains(_searchQuery.toLowerCase()) ||
                        booking.subtitle
                            .toLowerCase()
                            .contains(_searchQuery.toLowerCase()) ||
                        booking.location
                            .toLowerCase()
                            .contains(_searchQuery.toLowerCase()) ||
                        (booking.country
                                ?.toLowerCase()
                                .contains(_searchQuery.toLowerCase()) ??
                            false))
                    .toList();

            if (searchFiltered.isEmpty) {
              return _buildEmptyState(filter);
            }

            return ListView.builder(
              padding: const EdgeInsets.fromLTRB(
                PremiumDesignTokens.spacing12,
                PremiumDesignTokens.spacing8,
                PremiumDesignTokens.spacing12,
                PremiumDesignTokens.spacing24,
              ),
              itemCount: searchFiltered.length,
              physics: const BouncingScrollPhysics(),
              itemBuilder: (context, index) {
                final booking = searchFiltered[index];

                return TweenAnimationBuilder<double>(
                  duration: Duration(milliseconds: 200 + (index * 50)),
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  curve: Curves.easeOutCubic,
                  builder: (context, animationValue, child) {
                    return Transform.translate(
                      offset: Offset(0, 30 * (1 - animationValue)),
                      child: Opacity(
                        opacity: animationValue.clamp(0.0, 1.0),
                        child: Container(
                          margin: const EdgeInsets.only(
                              bottom: PremiumDesignTokens.spacing12),
                          child: PremiumBookingCard(
                            booking: booking,
                            width: double.infinity,
                            height: 200,
                            onTap: () => _handleBookingTap(booking),
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                  PremiumDesignTokens.primaryBlue),
              strokeWidth: 3,
            ),
          ),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildEmptyState(String filter) {
    String title;
    String message;
    IconData icon;

    switch (filter) {
      case 'upcoming':
        title = 'No Upcoming Adventures';
        message =
            'Your next amazing journey is just a booking away. Start exploring!';
        icon = Icons.schedule_rounded;
        break;
      case 'completed':
        title = 'No Completed Journeys';
        message =
            'Your travel memories will appear here once you complete your adventures.';
        icon = Icons.done_all_rounded;
        break;
      case 'cancelled':
        title = 'No Cancelled Bookings';
        message = 'Great! You haven\'t cancelled any adventures recently.';
        icon = Icons.cancel_rounded;
        break;
      default:
        title = 'No Bookings Found';
        message = 'Try adjusting your search or explore new destinations.';
        icon = Icons.search_off_rounded;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(PremiumDesignTokens.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    PremiumDesignTokens.primaryBlue.withValues(alpha: 0.2),
                    PremiumDesignTokens.accentTeal.withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: PremiumDesignTokens.primaryBlue,
              ),
            ),
            const SizedBox(height: PremiumDesignTokens.spacing24),
            Text(
              title,
              style: PremiumDesignTokens.headlineLarge.copyWith(
                color: PremiumDesignTokens.neutralGray900,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PremiumDesignTokens.spacing12),
            Text(
              message,
              style: PremiumDesignTokens.bodyLarge.copyWith(
                color: PremiumDesignTokens.neutralGray600,
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(PremiumDesignTokens.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: PremiumDesignTokens.statusError.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: PremiumDesignTokens.statusError,
              ),
            ),
            const SizedBox(height: PremiumDesignTokens.spacing24),
            Text(
              'Something went wrong',
              style: PremiumDesignTokens.headlineLarge.copyWith(
                color: PremiumDesignTokens.statusError,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PremiumDesignTokens.spacing12),
            Text(
              'Unable to load your bookings. Please try again.',
              style: PremiumDesignTokens.bodyLarge.copyWith(
                color: PremiumDesignTokens.neutralGray600,
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PremiumDesignTokens.spacing24),
            ElevatedButton(
              onPressed: _handleRefresh,
              style: ElevatedButton.styleFrom(
                backgroundColor: PremiumDesignTokens.primaryBlue,
                padding: const EdgeInsets.symmetric(
                  horizontal: PremiumDesignTokens.spacing24,
                  vertical: PremiumDesignTokens.spacing16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(PremiumDesignTokens.radiusMedium),
                ),
                elevation: 4,
              ),
              child: Text(
                'Try Again',
                style: PremiumDesignTokens.labelLarge.copyWith(
                  color: PremiumDesignTokens.neutralWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Handler methods
  void _handleBookingTap(PremiumBooking booking) {
    // Navigate to adaptive booking details screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AdaptiveBookingDetailsScreen(booking: booking),
      ),
    );
  }
}

/// Premium booking service for data management
class PremiumBookingService {
  Future<List<PremiumBooking>> getAllBookings() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    return PremiumMockData.getAllBookings();
  }

  Future<PremiumBookingStats> getBookingStats() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));
    return PremiumMockData.getBookingStats();
  }

  Future<List<PremiumBooking>> getBookingsByStatus(
      PremiumBookingStatus status) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return PremiumMockData.getBookingsByStatus(status);
  }

  Future<PremiumBooking?> getBookingById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return PremiumMockData.getBookingById(id);
  }
}
