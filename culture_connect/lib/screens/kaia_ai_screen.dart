import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';
import 'package:culture_connect/models/kaia_models.dart';

/// Kaia AI Screen - AI-powered travel assistant
/// Matches React Native Kaia tab functionality
class KaiaAIScreen extends ConsumerStatefulWidget {
  const KaiaAIScreen({super.key});

  @override
  ConsumerState<KaiaAIScreen> createState() => _KaiaAIScreenState();
}

class _KaiaAIScreenState extends ConsumerState<KaiaAIScreen>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;

  // Voice input state
  bool _isVoiceMode = false;
  bool _isListening = false;
  String _recognizedText = '';

  // Input area visibility control
  bool _showInputArea = false;
  bool _showInputField =
      true; // Controls input field visibility within input area

  // Animation controllers
  late AnimationController _voiceAnimationController;
  late AnimationController _inputModeController;
  late AnimationController _inputAreaController;
  late AnimationController _waveAnimationController;
  late AnimationController _toggleCollapseController;
  late Animation<double> _voicePulseAnimation;
  late Animation<double> _inputModeAnimation;
  late Animation<double> _inputAreaAnimation;
  late Animation<double> _waveAnimation;
  late Animation<double> _toggleCollapseAnimation;

  // Voice visualization state
  List<double> _audioLevels = List.filled(8, 0.0);
  double _currentAudioLevel = 0.0;

  // Toggle section collapse state
  bool _isToggleSectionCollapsed = false;

  // Floating quick actions position state
  Offset _quickActionsPosition =
      const Offset(0, 100); // Default position (right side, top: 100)

  // Chat scroll controller for auto-scroll behavior
  late ScrollController _chatScrollController;

  // Voice service
  ARVoiceCommandService? _voiceService;

  @override
  void initState() {
    super.initState();
    _addWelcomeMessage();
    _initializeAnimations();
    _initializeVoiceService();
    _chatScrollController = ScrollController();

    // Add text change listener for send button state management
    _messageController.addListener(() {
      setState(() {
        // Trigger rebuild to update send button state
      });
    });
  }

  void _initializeAnimations() {
    _voiceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _inputModeController = AnimationController(
      duration:
          const Duration(milliseconds: 150), // Reduced for snappy response
      vsync: this,
    );

    // Input area visibility animation
    _inputAreaController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Wave animation for enhanced voice visualization
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    // Toggle section collapse animation
    _toggleCollapseController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _voicePulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _voiceAnimationController,
      curve: Curves.easeInOut,
    ));

    _inputModeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _inputModeController,
      curve: Curves.easeInOut,
    ));

    _inputAreaAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _inputAreaController,
      curve: Curves.easeInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveAnimationController,
      curve: Curves.easeInOut,
    ));

    _toggleCollapseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toggleCollapseController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeVoiceService() async {
    try {
      _voiceService = ARVoiceCommandService();

      // Initialize with proper error handling
      final initialized = await _voiceService!.initialize();
      if (!initialized) {
        debugPrint(
            'Voice service initialization failed - permissions may not be granted');
        // Don't show error immediately, let user discover voice features naturally
        _voiceService = null;
        return;
      }

      // Add voice recognition listeners only if initialization succeeded
      _voiceService?.addRecognitionListener(_onVoiceRecognition);
      _voiceService?.addListeningStateListener(_onListeningStateChanged);

      debugPrint('Voice service initialized successfully for Kaia AI');
    } catch (e) {
      debugPrint('Exception during voice service initialization: $e');
      _voiceService = null;
      // Don't show error during initialization to avoid disrupting login flow
    }
  }

  void _onVoiceRecognition(String text) {
    if (mounted) {
      setState(() {
        _recognizedText = text;
      });
    }
  }

  void _onListeningStateChanged(bool isListening) {
    if (mounted) {
      setState(() {
        _isListening = isListening;
      });
    }
  }

  final List<KaiaQuickAction> _quickActions = [
    KaiaQuickAction(
      id: 'destination',
      title: 'Find Destinations',
      description: 'Discover places to visit', // Condensed
      icon: Icons.map_outlined,
      prompt:
          'Help me find the perfect travel destination based on my preferences',
      gradient: AppTheme.primaryGradient,
    ),
    KaiaQuickAction(
      id: 'itinerary',
      title: 'Plan Itinerary',
      description: 'Create detailed travel plans', // Condensed
      icon: Icons.calendar_today_outlined,
      prompt: 'Create a detailed itinerary for my upcoming trip',
      gradient: AppTheme.secondaryGradient,
    ),
    KaiaQuickAction(
      id: 'flights',
      title: 'Flight Advice',
      description: 'Get flight tips and deals', // Condensed
      icon: Icons.flight_outlined,
      prompt: 'Give me advice on finding the best flights for my trip',
      gradient: AppTheme.accentGradient,
    ),
    KaiaQuickAction(
      id: 'accommodation',
      title: 'Hotel Recommendations',
      description: 'Find perfect accommodations', // Condensed
      icon: Icons.hotel_outlined,
      prompt: 'Help me find the perfect accommodation for my trip',
      gradient: AppTheme.primaryGradient,
    ),
  ];

  @override
  void dispose() {
    _messageController.dispose();
    _voiceAnimationController.dispose();
    _inputModeController.dispose();
    _inputAreaController.dispose();
    _waveAnimationController.dispose();
    _toggleCollapseController.dispose();
    _chatScrollController.dispose();

    // Clean up voice service listeners
    _voiceService?.removeRecognitionListener(_onVoiceRecognition);
    _voiceService?.removeListeningStateListener(_onListeningStateChanged);
    _voiceService?.dispose();

    super.dispose();
  }

  void _addWelcomeMessage() {
    _messages.add(
      ChatMessage(
        text:
            "Hi! I'm Kaia, your AI travel assistant. How can I help you plan your next adventure?",
        isUser: false,
        timestamp: DateTime.now(),
      ),
    );
  }

  // Voice input methods
  void _toggleInputMode() {
    // Check if voice service is available before switching to voice mode
    if (!_isVoiceMode && _voiceService == null) {
      _showVoiceError(
          'Voice recognition is not available. Please check microphone permissions.');
      return;
    }

    setState(() {
      _isVoiceMode = !_isVoiceMode;
    });

    if (_isVoiceMode) {
      _inputModeController.forward();
      _startVoiceInput();
    } else {
      _inputModeController.reverse();
      _stopVoiceInput();
    }

    // Haptic feedback for mode switching
    HapticFeedback.mediumImpact();
  }

  void _startVoiceInput() async {
    if (_voiceService == null) {
      _showVoiceError('Voice service not available');
      return;
    }

    try {
      final success = await _voiceService!.startListening();
      if (success) {
        setState(() {
          _isListening = true;
          _recognizedText = ''; // Clear previous recognition
        });
        _voiceAnimationController.repeat(reverse: true);
        _startAudioLevelSimulation();
        HapticFeedback.lightImpact();
      } else {
        _showVoiceError(
            'Failed to start voice recognition. Please check microphone permissions.');
      }
    } catch (e) {
      debugPrint('Error starting voice input: $e');
      _showVoiceError('Voice recognition error. Please try again.');
    }
  }

  // Non-blocking voice input initialization for instant UI response
  void _startVoiceInputAsync() async {
    // Small delay to allow UI animation to start first
    await Future.delayed(const Duration(milliseconds: 50));
    _startVoiceInput();
  }

  // Toggle section collapse/expand functionality
  void _toggleCollapseSection() {
    setState(() {
      _isToggleSectionCollapsed = !_isToggleSectionCollapsed;
    });

    if (_isToggleSectionCollapsed) {
      _toggleCollapseController.forward();
    } else {
      _toggleCollapseController.reverse();
    }

    // Haptic feedback for toggle action
    HapticFeedback.lightImpact();

    // Maintain scroll position after toggle animation completes
    Future.delayed(const Duration(milliseconds: 350), () {
      if (_chatScrollController.hasClients && _messages.length > 1) {
        _scrollToBottom(animated: false);
      }
    });
  }

  // Auto-scroll to bottom of chat when new messages arrive
  void _scrollToBottom({bool animated = true}) {
    if (_chatScrollController.hasClients) {
      if (animated) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
      } else {
        _chatScrollController.jumpTo(
          _chatScrollController.position.maxScrollExtent,
        );
      }
    }
  }

  void _stopVoiceInput() async {
    if (_voiceService == null) return;

    try {
      await _voiceService!.stopListening();
      setState(() {
        _isListening = false;
      });
      _voiceAnimationController.stop();
      _voiceAnimationController.reset();
      _stopAudioLevelSimulation();
      HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Error stopping voice input: $e');
      setState(() {
        _isListening = false;
      });
      _voiceAnimationController.stop();
      _voiceAnimationController.reset();
      _stopAudioLevelSimulation();
    }
  }

  // Audio level simulation for enhanced voice visualization
  void _startAudioLevelSimulation() {
    _waveAnimationController.repeat();
    _simulateAudioLevels();
  }

  void _stopAudioLevelSimulation() {
    _waveAnimationController.stop();
    _waveAnimationController.reset();
    setState(() {
      _audioLevels = List.filled(8, 0.0);
      _currentAudioLevel = 0.0;
    });
  }

  void _simulateAudioLevels() async {
    while (_isListening && mounted) {
      await Future.delayed(const Duration(milliseconds: 100));
      if (_isListening && mounted) {
        setState(() {
          // Simulate realistic audio levels with some randomness
          _currentAudioLevel = 0.3 + (math.Random().nextDouble() * 0.7);

          // Update wave bars with varying heights
          for (int i = 0; i < _audioLevels.length; i++) {
            _audioLevels[i] = 0.2 + (math.Random().nextDouble() * 0.8);
          }
        });
      }
    }
  }

  void _showVoiceError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
      ),
    );
  }

  // ==================== CONVERSATION MANAGEMENT ====================

  void _startNewConversation() {
    // Enhanced haptic feedback for important action
    HapticFeedback.mediumImpact();

    setState(() {
      // Clear all messages except welcome message
      _messages.clear();
      _addWelcomeMessage();

      // Reset voice input state
      _isVoiceMode = false;
      _isListening = false;
      _recognizedText = '';
      _isTyping = false;

      // Clear text input
      _messageController.clear();
    });

    // Reset animation controllers
    _inputModeController.reset();
    _voiceAnimationController.reset();

    // Stop any ongoing voice recognition
    _stopVoiceInput();
  }

  @override
  Widget build(BuildContext context) {
    final hasConversation = _messages.length > 1;

    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralGray50,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Background gradient
          _buildBackgroundGradient(),

          // Main content
          SafeArea(
            child: hasConversation
                ? _buildConversationMode(context)
                : _buildWelcomeMode(context),
          ),

          // Floating quick actions (when in conversation)
          if (hasConversation) _buildFloatingQuickActions(),

          // Input area toggle button (when input area is hidden)
          if (!_showInputArea && !hasConversation) _buildInputToggleButton(),

          // Enhanced input area (conditionally shown)
          if (_showInputArea || hasConversation)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: AnimatedBuilder(
                animation: _inputAreaAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                        0,
                        hasConversation
                            ? 0
                            : (1 - _inputAreaAnimation.value) * 100),
                    child: Opacity(
                      opacity:
                          hasConversation ? 1.0 : _inputAreaAnimation.value,
                      child: _buildPremiumInputArea(),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  // ==================== INPUT AREA CONTROL METHODS ====================

  void _showInputAreaWithAnimation() {
    setState(() {
      _showInputArea = true;
    });
    _inputAreaController.forward();
  }

  void _hideInputAreaWithAnimation() {
    _inputAreaController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showInputArea = false;
        });
      }
    });
  }

  void _toggleInputArea() {
    if (_showInputArea) {
      _hideInputAreaWithAnimation();
    } else {
      _showInputAreaWithAnimation();
    }
  }

  Widget _buildInputToggleButton() {
    return Positioned(
      right: 16,
      bottom: 16,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Call-to-action text
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: KaiaDesignTokens.spacing12,
              vertical: KaiaDesignTokens.spacing8,
            ),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralWhite,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
              boxShadow: KaiaDesignTokens.shadowMd,
            ),
            child: Text(
              'Chat with Kaia',
              style: TextStyle(
                fontSize: KaiaDesignTokens.fontSizeSm,
                fontWeight: KaiaDesignTokens.fontWeightMedium,
                color: KaiaDesignTokens.neutralGray700,
                fontFamily: KaiaDesignTokens.secondaryFontFamily,
              ),
            ),
          ),

          SizedBox(width: KaiaDesignTokens.spacing12),

          // Input toggle button
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _toggleInputArea();
            },
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                gradient: KaiaDesignTokens.heroGradient,
                borderRadius: BorderRadius.circular(28),
                boxShadow: KaiaDesignTokens.shadowLg,
              ),
              child: const Icon(
                Icons.keyboard_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // ==================== PREMIUM UI METHODS ====================

  Widget _buildBackgroundGradient() {
    return Container(
      decoration: const BoxDecoration(
        gradient: KaiaDesignTokens.conversationGradient,
      ),
    );
  }

  Widget _buildWelcomeMode(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available height with proper buffer for input area
        final inputAreaBuffer = _showInputArea ? 160 : 100;
        final availableHeight = constraints.maxHeight - inputAreaBuffer;

        return SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: IntrinsicHeight(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: availableHeight > 0 ? availableHeight : 400.0,
              ),
              child: Column(
                children: [
                  // Hero section with flexible height
                  Flexible(
                    flex: 2,
                    child: Container(
                      constraints: BoxConstraints(
                        minHeight: 180.0, // Reduced from 200.0
                        maxHeight: 320.0, // Reduced from 350.0
                      ),
                      child: _buildHeroSection(),
                    ),
                  ),

                  // Reduced spacer to move content upward
                  SizedBox(
                      height:
                          KaiaDesignTokens.spacing12), // Reduced from spacing20

                  // Quick actions with flexible layout
                  Flexible(
                    flex: 3,
                    child: _buildWelcomeQuickActions(),
                  ),

                  // Increased bottom spacing for better separation from input area
                  SizedBox(
                      height: KaiaDesignTokens
                          .spacing40), // Increased from spacing24
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeroSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(KaiaDesignTokens.spacing32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // AI Avatar with premium styling
          TweenAnimationBuilder<double>(
            duration: KaiaDesignTokens.animationSlow,
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 0.8 + (value * 0.2),
                child: Opacity(
                  opacity: value,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: KaiaDesignTokens.heroGradient,
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radius3Xl),
                      boxShadow: KaiaDesignTokens.shadowXl,
                    ),
                    child: Icon(
                      Icons.smart_toy_rounded,
                      size: 60,
                      color: KaiaDesignTokens.neutralWhite,
                    ),
                  ),
                ),
              );
            },
          ),

          SizedBox(
              height: KaiaDesignTokens.spacing16), // Reduced from spacing24

          // Welcome text with staggered animation
          TweenAnimationBuilder<double>(
            duration: KaiaDesignTokens.animationSlow,
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Meet Kaia',
                        style: TextStyle(
                          fontSize: KaiaDesignTokens.fontSize4Xl,
                          fontWeight: KaiaDesignTokens.fontWeightExtraBold,
                          color: KaiaDesignTokens.neutralGray900,
                          fontFamily: KaiaDesignTokens.primaryFontFamily,
                          height: KaiaDesignTokens.lineHeightTight,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(
                          height: KaiaDesignTokens.spacing8), // Reduced spacing
                      Text(
                        'Your AI Travel Assistant',
                        style: TextStyle(
                          fontSize:
                              KaiaDesignTokens.fontSizeLg, // Reduced from XL
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                          color: KaiaDesignTokens.neutralGray600,
                          fontFamily: KaiaDesignTokens.secondaryFontFamily,
                          height: KaiaDesignTokens.lineHeightNormal,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      // Removed the long description text to eliminate clustering
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeQuickActions() {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: KaiaDesignTokens.spacing20,
          vertical: KaiaDesignTokens.spacing16, // Reduced padding
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'How can I help you today?',
              style: TextStyle(
                fontSize: KaiaDesignTokens.fontSizeXl, // Reduced from 2Xl
                fontWeight: KaiaDesignTokens.fontWeightBold,
                color: KaiaDesignTokens.neutralGray900,
                fontFamily: KaiaDesignTokens.primaryFontFamily,
              ),
              textAlign: TextAlign.center,
            ),

            // Enhanced guidance text with better visibility
            SizedBox(
                height:
                    KaiaDesignTokens.spacing8), // Slightly increased spacing
            Text(
              'Choose your travel planning focus',
              style: TextStyle(
                fontSize: KaiaDesignTokens
                    .fontSizeSm, // Increased from fontSizeXs for better readability
                fontWeight: KaiaDesignTokens
                    .fontWeightMedium, // Increased from fontWeightRegular for emphasis
                color: KaiaDesignTokens
                    .neutralGray600, // Enhanced from neutralGray400 for better visibility
                fontFamily: KaiaDesignTokens.secondaryFontFamily,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: KaiaDesignTokens.spacing12), // Reduced spacing

            // Single-column layout for better spacing
            Column(
              children: _quickActions.asMap().entries.map((entry) {
                final index = entry.key;
                final action = entry.value;

                return TweenAnimationBuilder<double>(
                  duration: Duration(
                    milliseconds: 300 + (index * 150),
                  ),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.translate(
                      offset: Offset(0, 30 * (1 - value)),
                      child: Opacity(
                        opacity: value,
                        child: Container(
                          margin: EdgeInsets.only(
                              bottom: KaiaDesignTokens
                                  .spacing12), // Reduced spacing
                          child: _buildFullWidthQuickActionCard(action),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
            ),

            // Reduced bottom spacing
            SizedBox(height: KaiaDesignTokens.spacing8),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationMode(BuildContext context) {
    return Column(
      children: [
        // Compact header
        _buildConversationHeader(),

        // Chat messages - no fixed spacing needed as ListView handles its own padding
        Expanded(
          child: _buildPremiumChatArea(),
        ),
      ],
    );
  }

  Widget _buildFullWidthQuickActionCard(KaiaQuickAction action) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _handleQuickAction(action);
      },
      child: Container(
        width: double.infinity,
        height: 64, // Reduced height for more compact design
        decoration: BoxDecoration(
          gradient: action.gradient,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusXl),
          boxShadow: KaiaDesignTokens.shadowMd,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusXl),
            onTap: () {
              HapticFeedback.lightImpact();
              _handleQuickAction(action);
            },
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal:
                    KaiaDesignTokens.spacing16, // Reduced horizontal padding
                vertical:
                    KaiaDesignTokens.spacing12, // Reduced vertical padding
              ),
              child: Row(
                children: [
                  // Icon with background
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusMd),
                    ),
                    child: Icon(
                      action.icon,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),

                  SizedBox(width: KaiaDesignTokens.spacing16),

                  // Title only (cleaner design)
                  Expanded(
                    child: Text(
                      action.title,
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeLg,
                        fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                        color: Colors.white,
                        fontFamily: KaiaDesignTokens.primaryFontFamily,
                      ),
                    ),
                  ),

                  // Arrow indicator
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: Colors.white.withValues(alpha: 0.7),
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConversationHeader() {
    return Container(
      padding: EdgeInsets.all(KaiaDesignTokens.spacing16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Row(
        children: [
          // AI Avatar
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: KaiaDesignTokens.heroGradient,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
            ),
            child: Icon(
              Icons.smart_toy_rounded,
              color: Colors.white,
              size: 20,
            ),
          ),

          SizedBox(width: KaiaDesignTokens.spacing12),

          // Title and status
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Kaia AI',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeLg,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                    color: KaiaDesignTokens.neutralGray900,
                    fontFamily: KaiaDesignTokens.primaryFontFamily,
                  ),
                ),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _isTyping
                            ? KaiaDesignTokens.warningAmber
                            : KaiaDesignTokens.successGreen,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: KaiaDesignTokens.spacing6),
                    Text(
                      _isTyping ? 'Thinking...' : 'Online',
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeSm,
                        color: KaiaDesignTokens.neutralGray600,
                        fontFamily: KaiaDesignTokens.secondaryFontFamily,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // New conversation button
          _buildNewConversationButton(),
        ],
      ),
    );
  }

  Widget _buildNewConversationButton() {
    return TweenAnimationBuilder<double>(
      duration: KaiaDesignTokens.animationNormal,
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (value * 0.2),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: _startNewConversation,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: KaiaDesignTokens.heroGradient,
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
            boxShadow: KaiaDesignTokens.shadowMd,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
              onTap: _startNewConversation,
              child: Icon(
                Icons.add_rounded,
                color: Colors.white,
                size: 22,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPremiumChatArea() {
    // Calculate dynamic bottom padding based on input area state
    const baseInputHeight = 120.0; // Base input area height
    final toggleSectionHeight =
        _isToggleSectionCollapsed ? 60.0 : 120.0; // Toggle section height
    const additionalPadding = 40.0; // Extra breathing room
    final bottomPadding =
        baseInputHeight + toggleSectionHeight + additionalPadding;

    return Container(
      decoration: BoxDecoration(
        gradient: KaiaDesignTokens.conversationGradient,
      ),
      child: ListView.builder(
        controller: _chatScrollController,
        padding: EdgeInsets.only(
          left: KaiaDesignTokens.spacing16,
          right: KaiaDesignTokens.spacing16,
          top: KaiaDesignTokens.spacing16,
          bottom: bottomPadding, // Dynamic bottom padding
        ),
        itemCount: _messages.length + (_isTyping ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _messages.length && _isTyping) {
            return _buildPremiumTypingIndicator();
          }

          final message = _messages[index];
          return _buildPremiumMessageBubble(message);
        },
      ),
    );
  }

  Widget _buildPremiumMessageBubble(ChatMessage message) {
    return TweenAnimationBuilder<double>(
      duration: KaiaDesignTokens.animationNormal,
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
        child: Row(
          mainAxisAlignment:
              message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!message.isUser) ...[
              _buildPremiumAIAvatar(),
              SizedBox(width: KaiaDesignTokens.spacing12),
            ],
            Flexible(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing16,
                  vertical: KaiaDesignTokens.spacing12,
                ),
                decoration: BoxDecoration(
                  gradient: message.isUser
                      ? KaiaDesignTokens.messageUserGradient
                      : KaiaDesignTokens.messageAIGradient,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(KaiaDesignTokens.radiusXl),
                    topRight: Radius.circular(KaiaDesignTokens.radiusXl),
                    bottomLeft: Radius.circular(
                      message.isUser
                          ? KaiaDesignTokens.radiusXl
                          : KaiaDesignTokens.radiusMd,
                    ),
                    bottomRight: Radius.circular(
                      message.isUser
                          ? KaiaDesignTokens.radiusMd
                          : KaiaDesignTokens.radiusXl,
                    ),
                  ),
                  boxShadow: KaiaDesignTokens.shadowMd,
                  border: message.isUser
                      ? null
                      : Border.all(
                          color: KaiaDesignTokens.neutralGray200,
                          width: 1,
                        ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.text,
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightRegular,
                        color: message.isUser
                            ? Colors.white
                            : KaiaDesignTokens.neutralGray900,
                        fontFamily: KaiaDesignTokens.secondaryFontFamily,
                        height: KaiaDesignTokens.lineHeightNormal,
                      ),
                    ),
                    SizedBox(height: KaiaDesignTokens.spacing6),
                    Text(
                      _formatTimestamp(message.timestamp),
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeXs,
                        color: message.isUser
                            ? Colors.white.withValues(alpha: 0.7)
                            : KaiaDesignTokens.neutralGray500,
                        fontFamily: KaiaDesignTokens.secondaryFontFamily,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (message.isUser) ...[
              SizedBox(width: KaiaDesignTokens.spacing12),
              _buildPremiumUserAvatar(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumTypingIndicator() {
    return TweenAnimationBuilder<double>(
      duration: KaiaDesignTokens.animationNormal,
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
        child: Row(
          children: [
            _buildPremiumAIAvatar(),
            SizedBox(width: KaiaDesignTokens.spacing12),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: KaiaDesignTokens.spacing16,
                vertical: KaiaDesignTokens.spacing12,
              ),
              decoration: BoxDecoration(
                gradient: KaiaDesignTokens.messageAIGradient,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(KaiaDesignTokens.radiusXl),
                  topRight: Radius.circular(KaiaDesignTokens.radiusXl),
                  bottomLeft: Radius.circular(KaiaDesignTokens.radiusMd),
                  bottomRight: Radius.circular(KaiaDesignTokens.radiusXl),
                ),
                boxShadow: KaiaDesignTokens.shadowMd,
                border: Border.all(
                  color: KaiaDesignTokens.neutralGray200,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildPremiumTypingDot(0),
                  SizedBox(width: KaiaDesignTokens.spacing6),
                  _buildPremiumTypingDot(1),
                  SizedBox(width: KaiaDesignTokens.spacing6),
                  _buildPremiumTypingDot(2),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumAIAvatar() {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        gradient: KaiaDesignTokens.heroGradient,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
        boxShadow: [
          BoxShadow(
            color: KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        Icons.smart_toy_rounded,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  Widget _buildPremiumUserAvatar() {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.8),
            KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
        boxShadow: [
          BoxShadow(
            color: KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        Icons.person_rounded,
        size: 18,
        color: KaiaDesignTokens.primaryIndigo,
      ),
    );
  }

  Widget _buildPremiumTypingDot(int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + (index * 200)),
      tween: Tween(begin: 0.3, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.5 + (value * 0.5),
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: KaiaDesignTokens.primaryIndigo.withValues(alpha: value),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildFloatingQuickActions() {
    final screenSize = MediaQuery.of(context).size;

    return Positioned(
      top: _quickActionsPosition.dy,
      right: _quickActionsPosition.dx == 0 ? KaiaDesignTokens.spacing16 : null,
      left: _quickActionsPosition.dx != 0 ? _quickActionsPosition.dx : null,
      child: Draggable<bool>(
        feedback: Material(
          color: Colors.transparent,
          child: _buildQuickActionButtons(isDragging: true),
        ),
        childWhenDragging: _buildQuickActionButtons(isChildWhenDragging: true),
        onDragEnd: (details) {
          setState(() {
            // Calculate new position with boundary constraints
            final buttonGroupHeight = (_quickActions.length * 52) +
                ((_quickActions.length - 1) * 12); // 40px button + 12px margin
            final buttonWidth = 56.0; // 40px button + 16px padding

            _quickActionsPosition = Offset(
              // Constrain horizontal position
              math.max(0,
                  math.min(details.offset.dx, screenSize.width - buttonWidth)),
              // Constrain vertical position
              math.max(
                  100,
                  math.min(details.offset.dy,
                      screenSize.height - buttonGroupHeight - 200)),
            );
          });
        },
        child: _buildQuickActionButtons(),
      ),
    );
  }

  Widget _buildQuickActionButtons(
      {bool isDragging = false, bool isChildWhenDragging = false}) {
    return Column(
      children: [
        for (int i = 0; i < _quickActions.length; i++)
          TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 300 + (i * 100)),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Opacity(
                  opacity: isChildWhenDragging ? 0.5 : value,
                  child: Container(
                    margin: EdgeInsets.only(bottom: KaiaDesignTokens.spacing12),
                    child: FloatingActionButton(
                      heroTag: "fab_${_quickActions[i].id}",
                      onPressed: isChildWhenDragging
                          ? null
                          : () => _handleQuickAction(_quickActions[i]),
                      backgroundColor: Colors.transparent,
                      elevation: isDragging ? 8 : 0,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          gradient: _quickActions[i].gradient,
                          borderRadius: BorderRadius.circular(
                              KaiaDesignTokens.radiusFull),
                          boxShadow: isDragging
                              ? KaiaDesignTokens.shadowXl
                              : KaiaDesignTokens.shadowMd,
                        ),
                        child: Icon(
                          _quickActions[i].icon,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildPremiumInputArea() {
    final hasConversation = _messages.length > 1;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(KaiaDesignTokens.spacing16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with close button (only in welcome mode)
              if (!hasConversation)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Chat with Kaia',
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightMedium,
                        color: KaiaDesignTokens.neutralGray700,
                        fontFamily: KaiaDesignTokens.primaryFontFamily,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        _hideInputAreaWithAnimation();
                      },
                      child: Container(
                        padding: EdgeInsets.all(KaiaDesignTokens.spacing6),
                        decoration: BoxDecoration(
                          color: KaiaDesignTokens.neutralGray100,
                          borderRadius: BorderRadius.circular(
                              KaiaDesignTokens.radiusFull),
                        ),
                        child: Icon(
                          Icons.close_rounded,
                          size: 18,
                          color: KaiaDesignTokens.neutralGray600,
                        ),
                      ),
                    ),
                  ],
                ),

              if (!hasConversation)
                SizedBox(height: KaiaDesignTokens.spacing12),

              // Collapsible toggle section
              _buildCollapsibleToggleSection(hasConversation),

              // Input field (conditionally shown)
              if (_showInputField || !hasConversation) ...[
                SizedBox(height: KaiaDesignTokens.spacing12),
                _buildPremiumInputField(),
              ],

              // Show/hide input field toggle for conversation mode
              if (hasConversation && !_showInputField)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showInputField = true;
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: KaiaDesignTokens.spacing8),
                    padding: EdgeInsets.symmetric(
                      horizontal: KaiaDesignTokens.spacing12,
                      vertical: KaiaDesignTokens.spacing6,
                    ),
                    decoration: BoxDecoration(
                      color: KaiaDesignTokens.neutralGray100,
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusLg),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.keyboard_rounded,
                          size: 16,
                          color: KaiaDesignTokens.neutralGray600,
                        ),
                        SizedBox(width: KaiaDesignTokens.spacing4),
                        Text(
                          'Show input field',
                          style: TextStyle(
                            fontSize: KaiaDesignTokens.fontSizeSm,
                            color: KaiaDesignTokens.neutralGray600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCollapsibleToggleSection(bool hasConversation) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Collapse/Expand button
        GestureDetector(
          onTap: _toggleCollapseSection,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: KaiaDesignTokens.spacing12,
              vertical: KaiaDesignTokens.spacing6,
            ),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralGray100,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _isToggleSectionCollapsed ? 'Show Options' : 'Hide Options',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeXs,
                    fontWeight: KaiaDesignTokens.fontWeightMedium,
                    color: KaiaDesignTokens.neutralGray600,
                    fontFamily: KaiaDesignTokens.secondaryFontFamily,
                  ),
                ),
                SizedBox(width: KaiaDesignTokens.spacing4),
                AnimatedRotation(
                  turns: _isToggleSectionCollapsed ? 0.5 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.keyboard_arrow_up_rounded,
                    size: 16,
                    color: KaiaDesignTokens.neutralGray600,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Collapsible toggle content
        AnimatedBuilder(
          animation: _toggleCollapseAnimation,
          builder: (context, child) {
            return ClipRect(
              child: Align(
                alignment: Alignment.topCenter,
                heightFactor: _isToggleSectionCollapsed
                    ? 1.0 - _toggleCollapseAnimation.value
                    : 1.0,
                child: child,
              ),
            );
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: KaiaDesignTokens.spacing8),

              // Voice/Text mode toggle
              _buildPremiumModeToggle(),

              // Guidance text for input area
              if (!hasConversation) ...[
                SizedBox(height: KaiaDesignTokens.spacing8),
                Text(
                  'Choose your preferred way to communicate with Kaia',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeXs,
                    color: KaiaDesignTokens.neutralGray500,
                    fontFamily: KaiaDesignTokens.secondaryFontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPremiumModeToggle() {
    return Container(
      padding: EdgeInsets.all(KaiaDesignTokens.spacing4),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralGray100,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusXl),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModeToggleButton(
            icon: Icons.keyboard_rounded,
            label: 'Type',
            isActive: !_isVoiceMode,
            onTap: () {
              if (_isVoiceMode) {
                // Immediate state change for snappy response
                setState(() => _isVoiceMode = false);
                // Animation runs in parallel
                _inputModeController.reverse();
                HapticFeedback.selectionClick();
              }
            },
          ),
          SizedBox(width: KaiaDesignTokens.spacing4),
          _buildModeToggleButton(
            icon: Icons.mic_rounded,
            label: 'Voice',
            isActive: _isVoiceMode,
            onTap: () {
              if (!_isVoiceMode) {
                // Immediate state change for snappy response
                setState(() => _isVoiceMode = true);
                // Animation runs immediately for instant feedback
                _inputModeController.forward();
                HapticFeedback.selectionClick();
                // Voice input initialization runs in parallel without blocking UI
                _startVoiceInputAsync();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildModeToggleButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: KaiaDesignTokens.animationFast,
        padding: EdgeInsets.symmetric(
          horizontal: KaiaDesignTokens.spacing16,
          vertical: KaiaDesignTokens.spacing8,
        ),
        decoration: BoxDecoration(
          color: isActive ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          boxShadow: isActive ? KaiaDesignTokens.shadowSm : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isActive
                  ? KaiaDesignTokens.primaryIndigo
                  : KaiaDesignTokens.neutralGray600,
            ),
            SizedBox(width: KaiaDesignTokens.spacing6),
            Text(
              label,
              style: TextStyle(
                fontSize: KaiaDesignTokens.fontSizeSm,
                fontWeight: isActive
                    ? KaiaDesignTokens.fontWeightSemiBold
                    : KaiaDesignTokens.fontWeightRegular,
                color: isActive
                    ? KaiaDesignTokens.primaryIndigo
                    : KaiaDesignTokens.neutralGray600,
                fontFamily: KaiaDesignTokens.secondaryFontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumInputField() {
    return Container(
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralGray50,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusXl),
        border: Border.all(
          color: KaiaDesignTokens.neutralGray200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _isVoiceMode
                ? _buildVoiceInputDisplay()
                : _buildTextInputField(),
          ),
          _buildPremiumSendButton(),
        ],
      ),
    );
  }

  Widget _buildTextInputField() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: KaiaDesignTokens.spacing16,
        vertical: KaiaDesignTokens.spacing12,
      ),
      child: TextField(
        controller: _messageController,
        decoration: InputDecoration(
          hintText: 'Ask me anything about travel...',
          hintStyle: TextStyle(
            color: KaiaDesignTokens.neutralGray500,
            fontSize: KaiaDesignTokens.fontSizeMd,
            fontFamily: KaiaDesignTokens.secondaryFontFamily,
          ),
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        style: TextStyle(
          fontSize: KaiaDesignTokens.fontSizeMd,
          color: KaiaDesignTokens.neutralGray900,
          fontFamily: KaiaDesignTokens.secondaryFontFamily,
        ),
        maxLines: null,
        textCapitalization: TextCapitalization.sentences,
      ),
    );
  }

  Widget _buildVoiceInputDisplay() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: KaiaDesignTokens.spacing16,
        vertical: KaiaDesignTokens.spacing12,
      ),
      child: _isListening
          ? _buildEnhancedVoiceVisualization()
          : _buildVoicePrompt(),
    );
  }

  Widget _buildVoicePrompt() {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: KaiaDesignTokens.neutralGray200,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.mic_rounded,
            size: 18,
            color: KaiaDesignTokens.neutralGray600,
          ),
        ),
        SizedBox(width: KaiaDesignTokens.spacing12),
        Expanded(
          child: Text(
            'Tap to speak with Kaia',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeSm,
              fontWeight: KaiaDesignTokens.fontWeightMedium,
              color: KaiaDesignTokens.neutralGray600,
              fontFamily: KaiaDesignTokens.secondaryFontFamily,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedVoiceVisualization() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Status and microphone icon
        Row(
          children: [
            // Animated microphone with pulse effect
            AnimatedBuilder(
              animation: _voicePulseAnimation,
              builder: (context, child) {
                return Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: KaiaDesignTokens.heroGradient,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: KaiaDesignTokens.primaryIndigo
                            .withValues(alpha: 0.3),
                        blurRadius: 8 * _voicePulseAnimation.value,
                        spreadRadius: 2 * _voicePulseAnimation.value,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.mic_rounded,
                    size: 20,
                    color: Colors.white,
                  ),
                );
              },
            ),

            SizedBox(width: KaiaDesignTokens.spacing16),

            // Wave visualization
            Expanded(
              child: _buildWaveVisualization(),
            ),

            SizedBox(width: KaiaDesignTokens.spacing16),

            // Status text
            Text(
              'Listening...',
              style: TextStyle(
                fontSize: KaiaDesignTokens.fontSizeSm,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                color: KaiaDesignTokens.primaryIndigo,
                fontFamily: KaiaDesignTokens.secondaryFontFamily,
              ),
            ),
          ],
        ),

        // Recognized text display
        if (_recognizedText.isNotEmpty) ...[
          SizedBox(height: KaiaDesignTokens.spacing8),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(KaiaDesignTokens.spacing8),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
              border: Border.all(
                color: KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Text(
              _recognizedText,
              style: TextStyle(
                fontSize: KaiaDesignTokens.fontSizeSm,
                color: KaiaDesignTokens.primaryIndigo,
                fontFamily: KaiaDesignTokens.secondaryFontFamily,
                fontStyle: FontStyle.italic,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildWaveVisualization() {
    return AnimatedBuilder(
      animation: _waveAnimation,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(8, (index) {
            final height = 4.0 + (_audioLevels[index] * 20.0);
            return AnimatedContainer(
              duration: const Duration(milliseconds: 100),
              width: 3,
              height: height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    KaiaDesignTokens.primaryIndigo,
                    KaiaDesignTokens.secondaryCyan,
                  ],
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildPremiumSendButton() {
    final hasContent = _isVoiceMode
        ? _recognizedText.trim().isNotEmpty
        : _messageController.text.trim().isNotEmpty;

    return Container(
      margin: EdgeInsets.all(KaiaDesignTokens.spacing6),
      child: AnimatedContainer(
        duration: KaiaDesignTokens.animationFast,
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          gradient: hasContent
              ? KaiaDesignTokens.heroGradient
              : LinearGradient(
                  colors: [
                    KaiaDesignTokens.neutralGray300,
                    KaiaDesignTokens.neutralGray300,
                  ],
                ),
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
          boxShadow: hasContent ? KaiaDesignTokens.shadowMd : null,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
            onTap: hasContent ? _sendMessage : null,
            child: Icon(
              Icons.send_rounded,
              size: 20,
              color:
                  hasContent ? Colors.white : KaiaDesignTokens.neutralGray500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: AppTheme.spacingMd),
        child: Row(
          mainAxisAlignment:
              message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!message.isUser) ...[
              _buildAIAvatar(),
              const SizedBox(width: AppTheme.spacingSm),
            ],
            Flexible(
              child: _buildEnhancedMessageBubble(message),
            ),
            if (message.isUser) ...[
              const SizedBox(width: AppTheme.spacingSm),
              _buildUserAvatar(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAIAvatar() {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Icon(
        Icons.smart_toy_rounded,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.8),
            AppTheme.primaryColor.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        Icons.person_rounded,
        size: 18,
        color: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildEnhancedMessageBubble(ChatMessage message) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm + 2,
      ),
      decoration: BoxDecoration(
        gradient: message.isUser
            ? AppTheme.primaryGradient
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.surfaceColor,
                  AppTheme.surfaceColor.withValues(alpha: 0.95),
                ],
              ),
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(AppTheme.borderRadiusLarge),
          topRight: const Radius.circular(AppTheme.borderRadiusLarge),
          bottomLeft: Radius.circular(
            message.isUser
                ? AppTheme.borderRadiusLarge
                : AppTheme.borderRadiusMedium,
          ),
          bottomRight: Radius.circular(
            message.isUser
                ? AppTheme.borderRadiusMedium
                : AppTheme.borderRadiusLarge,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: message.isUser
                ? AppTheme.primaryColor.withValues(alpha: 0.2)
                : AppTheme.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message.text,
            style: TextStyle(
              color: message.isUser ? Colors.white : AppTheme.textPrimaryColor,
              fontSize: AppTheme.fontSizeMd,
              fontFamily: AppTheme.fontFamily,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _formatTimestamp(message.timestamp),
            style: TextStyle(
              color: message.isUser
                  ? Colors.white.withValues(alpha: 0.7)
                  : AppTheme.textSecondaryColor,
              fontSize: AppTheme.fontSizeXs,
              fontFamily: AppTheme.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  Widget _buildTypingIndicator() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: AppTheme.spacingMd),
        child: Row(
          children: [
            _buildAIAvatar(),
            const SizedBox(width: AppTheme.spacingSm),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMd,
                vertical: AppTheme.spacingSm + 2,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.surfaceColor,
                    AppTheme.surfaceColor.withValues(alpha: 0.95),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadiusLarge),
                  topRight: Radius.circular(AppTheme.borderRadiusLarge),
                  bottomLeft: Radius.circular(AppTheme.borderRadiusMedium),
                  bottomRight: Radius.circular(AppTheme.borderRadiusLarge),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.shadowColor,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildAnimatedDot(0),
                  const SizedBox(width: 6),
                  _buildAnimatedDot(1),
                  const SizedBox(width: 6),
                  _buildAnimatedDot(2),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedDot(int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + (index * 200)),
      tween: Tween(begin: 0.3, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.5 + (value * 0.5),
          child: Container(
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: value),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        children: [
          // Voice recognition feedback
          if (_isListening) _buildVoiceRecognitionFeedback(),

          // Main input row
          Row(
            children: [
              // Voice/Text toggle button
              _buildInputModeToggle(),

              const SizedBox(width: AppTheme.spacingSm),

              // Input field or voice indicator
              Expanded(
                child: _isVoiceMode
                    ? _buildVoiceInputArea()
                    : _buildTextInputArea(),
              ),

              const SizedBox(width: AppTheme.spacingSm),

              // Send button
              _buildSendButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceRecognitionFeedback() {
    return AnimatedBuilder(
      animation: _voicePulseAnimation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppTheme.spacingSm),
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingMd,
            vertical: AppTheme.spacingSm,
          ),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Transform.scale(
                scale: _voicePulseAnimation.value,
                child: Icon(
                  Icons.mic,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: Text(
                  _recognizedText.isEmpty
                      ? 'Listening... Speak now'
                      : _recognizedText,
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: AppTheme.fontSizeSm,
                    fontFamily: AppTheme.fontFamily,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInputModeToggle() {
    return AnimatedBuilder(
      animation: _inputModeAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: _toggleInputMode,
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: _isVoiceMode
                  ? AppTheme.secondaryGradient
                  : AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              boxShadow: AppTheme.shadowLight,
            ),
            child: Icon(
              _isVoiceMode ? Icons.keyboard : Icons.mic,
              color: Colors.white,
              size: 24,
            ),
          ),
        );
      },
    );
  }

  Widget _buildTextInputArea() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(color: AppTheme.outline),
      ),
      child: TextField(
        controller: _messageController,
        decoration: const InputDecoration(
          hintText: 'Ask Kaia anything...',
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(AppTheme.spacingMd),
        ),
        maxLines: null,
        textCapitalization: TextCapitalization.sentences,
        onSubmitted: (_) => _sendMessage(),
      ),
    );
  }

  Widget _buildVoiceInputArea() {
    return AnimatedBuilder(
      animation: _voicePulseAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: () {
            if (_isListening) {
              _stopVoiceInput();
            } else {
              _startVoiceInput();
            }
          },
          child: Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: _isListening
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              border: Border.all(
                color: _isListening ? AppTheme.primaryColor : AppTheme.outline,
                width: _isListening ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Transform.scale(
                  scale: _isListening ? _voicePulseAnimation.value : 1.0,
                  child: Icon(
                    _isListening ? Icons.mic : Icons.mic_none,
                    color: _isListening
                        ? AppTheme.primaryColor
                        : AppTheme.textSecondaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _isListening ? 'Listening...' : 'Tap to speak',
                        style: TextStyle(
                          color: _isListening
                              ? AppTheme.primaryColor
                              : AppTheme.textSecondaryColor,
                          fontSize: AppTheme.fontSizeMd,
                          fontFamily: AppTheme.fontFamily,
                          fontWeight: _isListening
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                      if (_recognizedText.isNotEmpty && _isListening)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            _recognizedText,
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: AppTheme.fontSizeSm,
                              fontFamily: AppTheme.fontFamily,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSendButton() {
    final hasText = _messageController.text.trim().isNotEmpty;
    final canSend = hasText || (_isVoiceMode && _recognizedText.isNotEmpty);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        gradient: canSend
            ? AppTheme.primaryGradient
            : LinearGradient(
                colors: [
                  AppTheme.textSecondaryColor,
                  AppTheme.textSecondaryColor,
                ],
              ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: IconButton(
        onPressed: canSend ? _sendMessage : null,
        icon: const Icon(
          Icons.send_rounded,
          color: Colors.white,
        ),
      ),
    );
  }

  void _handleQuickAction(KaiaQuickAction action) {
    // Show input area if it's hidden (for welcome mode)
    if (!_showInputArea) {
      _showInputAreaWithAnimation();
    }

    // Add the prompt as a user message
    setState(() {
      _messages.add(
        ChatMessage(
          text: action.prompt,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      _isTyping = true;

      // When transitioning to conversation mode, initially hide input field
      if (_messages.length == 1) {
        // This will be the second message (first is welcome)
        _showInputField = false;
      }
    });

    // Auto-scroll to show the new user message
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });

    // Simulate AI response based on action type
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isTyping = false;
          _messages.add(
            ChatMessage(
              text: _generateActionResponse(action),
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        });

        // Auto-scroll to show the new AI response
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    });
  }

  String _generateActionResponse(KaiaQuickAction action) {
    switch (action.id) {
      case 'destination':
        return "I'd love to help you find the perfect destination! To give you the best recommendations, could you tell me:\n\n• What type of experience are you looking for? (adventure, relaxation, culture, etc.)\n• What's your budget range?\n• When are you planning to travel?\n• Any specific regions or climates you prefer?";
      case 'itinerary':
        return "Great! I can help you create a detailed itinerary. Let me know:\n\n• Your destination\n• Travel dates and duration\n• Your interests (museums, food, nightlife, nature, etc.)\n• Your travel style (fast-paced or relaxed)\n• Any must-see attractions or experiences";
      case 'flights':
        return "I can definitely help you find the best flight deals! Here are some expert tips:\n\n✈️ **Best booking times**: 6-8 weeks in advance for domestic, 2-3 months for international\n✈️ **Flexible dates**: Use fare comparison tools\n✈️ **Consider nearby airports**: Sometimes worth the extra travel\n✈️ **Clear cookies**: Airlines track your searches\n\nWhere are you planning to fly from and to?";
      case 'accommodation':
        return "I'll help you find the perfect place to stay! To give you the best recommendations:\n\n🏨 **What's your preferred accommodation type?** (hotel, Airbnb, hostel, resort)\n🏨 **Location preferences?** (city center, beach, quiet area)\n🏨 **Budget range per night?**\n🏨 **Important amenities?** (pool, gym, kitchen, etc.)\n🏨 **Travel dates?**";
      default:
        return "I'm here to help with your travel planning! What specific aspect would you like assistance with?";
    }
  }

  void _sendMessage() {
    String messageText;

    if (_isVoiceMode) {
      messageText = _recognizedText.trim();
      if (messageText.isEmpty) return;
    } else {
      messageText = _messageController.text.trim();
      if (messageText.isEmpty) return;
    }

    setState(() {
      _messages.add(
        ChatMessage(
          text: messageText,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      _isTyping = true;
    });

    // Auto-scroll to show the new user message
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });

    // Clear input based on mode with smooth transition
    if (_isVoiceMode) {
      _recognizedText = '';
      _stopVoiceInput();
      // Switch back to text mode after sending voice message
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isVoiceMode = false;
          });
          _inputModeController.reverse();
        }
      });
    } else {
      _messageController.clear();
    }

    // Enhanced haptic feedback for sending message
    HapticFeedback.mediumImpact();

    // Simulate AI response with realistic typing delay
    final responseDelay =
        Duration(milliseconds: 1500 + (messageText.length * 20));
    Future.delayed(responseDelay, () {
      if (mounted) {
        setState(() {
          _isTyping = false;
          _messages.add(
            ChatMessage(
              text: _generateAIResponse(messageText),
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        });

        // Auto-scroll to show the new AI response
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });

        // Subtle success haptic feedback
        HapticFeedback.lightImpact();
      }
    });
  }

  String _generateAIResponse(String userMessage) {
    // Simple AI response simulation
    final responses = [
      "That's a great question! I'd be happy to help you with your travel plans.",
      "Based on your interests, I recommend exploring some cultural experiences.",
      "Let me suggest some amazing destinations that match your preferences.",
      "I can help you find the perfect activities for your trip!",
      "Would you like me to create a personalized itinerary for you?",
    ];

    return responses[DateTime.now().millisecond % responses.length];
  }
}
